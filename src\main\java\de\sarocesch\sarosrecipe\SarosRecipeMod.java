package de.sarocesch.sarosrecipe;

import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;

import de.sarocesch.sarosrecipe.commands.RecipeCommand;

@Mod("sarosrecipe")
public class SarosRecipeMod {
    public static final String MODID = "sarosrecipe";

    public SarosRecipeMod() {
        System.out.println("<PERSON><PERSON>'s Recipe Mod is initializing...");

        // Register the setup method for modloading
        FMLJavaModLoadingContext.get().getModEventBus().addListener(this::setup);

        // Register ourselves for server and other game events we are interested in
        MinecraftForge.EVENT_BUS.register(this);
    }

    private void setup(final FMLCommonSetupEvent event) {
        System.out.println("<PERSON><PERSON>'s Recipe Mod setup complete");
    }

    @SubscribeEvent
    public void onRegisterCommands(RegisterCommandsEvent event) {
        System.out.println("Registering modrecipe commands");
        RecipeCommand.register(event.getDispatcher());
    }
}
