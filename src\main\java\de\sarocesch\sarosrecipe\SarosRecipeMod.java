package de.sarocesch.sarosrecipe;

import de.sarocesch.sarosrecipe.commands.RecipeCommand;

// @Mod(SarosRecipeMod.MODID) - TODO: Add back when mappings are resolved
public class SarosRecipeMod {
    public static final String MODID = "sarosrecipe";

    public SarosRecipeMod() {
        System.out.println("Saro's Recipe Mod is initializing...");

        // Placeholder for command registration
        RecipeCommand.register(null);

        // Placeholder for game rule management
        GameRuleManager.onServerStarted(null);
    }
}
