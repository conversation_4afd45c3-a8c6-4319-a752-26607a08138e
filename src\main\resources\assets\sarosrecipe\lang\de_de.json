{"commands.sarosrecipe.give.success.single": "Rezept %2$s erfolgreich an %1$s gegeben", "commands.sarosrecipe.give.success.mod": "%2$d Rezept<PERSON> von Mod %3$s erfolgreich an %1$s gegeben", "commands.sarosrecipe.take.success.single": "Rezept %2$s erfolg<PERSON>ich von %1$s entfernt", "commands.sarosrecipe.take.success.mod": "%2$d Rezepte von Mod %3$s erfolg<PERSON><PERSON> von %1$s entfernt", "commands.sarosrecipe.give.error": "Fehler beim Geben des Rezepts: %s", "commands.sarosrecipe.take.error": "Fehler beim Entfernen des Rezepts: %s", "commands.sarosrecipe.recipe.not_found": "Rezept nicht gefunden: %s", "commands.sarosrecipe.mod.no_recipes": "<PERSON>ine Rezepte für Mod gefunden: %s"}